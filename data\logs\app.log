{"timestamp": "2025-06-21T10:29:46.313292", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T10:29:46.332373", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T10:29:46.333941", "level": "ERROR", "logger": "app.core.database", "message": "数据库初始化失败: Not an executable object: 'SELECT 1'", "module": "database", "function": "init_database", "line": 79}
{"timestamp": "2025-06-21T10:33:11.563976", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T10:33:11.580372", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T10:33:11.580372", "level": "ERROR", "logger": "app.core.database", "message": "数据库初始化失败: Not an executable object: 'SELECT 1'", "module": "database", "function": "init_database", "line": 79}
{"timestamp": "2025-06-21T10:37:10.479724", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T10:37:10.496393", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T10:37:10.498399", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T10:40:41.407284", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T10:40:41.428773", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T10:40:41.429779", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T11:04:50.254047", "level": "ERROR", "logger": "app.services.auth_service", "message": "查询用户失败: (sqlite3.OperationalError) no such table: users\n[SQL: SELECT users.email AS users_email, users.hashed_password AS users_hashed_password, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.points AS users_points, users.last_login AS users_last_login, users.id AS users_id, users.created_at AS users_created_at, users.updated_at AS users_updated_at \nFROM users \nWHERE users.email = ?\n LIMIT ? OFFSET ?]\n[parameters: ('<EMAIL>', 1, 0)]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "auth_service", "function": "get_user_by_email", "line": 90}
{"timestamp": "2025-06-21T11:04:50.259226", "level": "ERROR", "logger": "app.services.auth_service", "message": "创建用户失败: (sqlite3.OperationalError) no such table: users\n[SQL: SELECT count(*) AS count_1 \nFROM (SELECT users.email AS users_email, users.hashed_password AS users_hashed_password, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.points AS users_points, users.last_login AS users_last_login, users.id AS users_id, users.created_at AS users_created_at, users.updated_at AS users_updated_at \nFROM users) AS anon_1]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "auth_service", "function": "create_user", "line": 134}
{"timestamp": "2025-06-21T11:04:50.260228", "level": "ERROR", "logger": "app.api.auth", "message": "用户注册失败: (sqlite3.OperationalError) no such table: users\n[SQL: SELECT count(*) AS count_1 \nFROM (SELECT users.email AS users_email, users.hashed_password AS users_hashed_password, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.points AS users_points, users.last_login AS users_last_login, users.id AS users_id, users.created_at AS users_created_at, users.updated_at AS users_updated_at \nFROM users) AS anon_1]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "auth", "function": "register", "line": 34}
{"timestamp": "2025-06-21T11:04:50.261248", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: (sqlite3.OperationalError) no such table: users\n[SQL: SELECT count(*) AS count_1 \nFROM (SELECT users.email AS users_email, users.hashed_password AS users_hashed_password, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.points AS users_points, users.last_login AS users_last_login, users.id AS users_id, users.created_at AS users_created_at, users.updated_at AS users_updated_at \nFROM users) AS anon_1]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T11:05:41.406152", "level": "ERROR", "logger": "app.api.auth", "message": "获取认证统计失败: (sqlite3.OperationalError) no such table: users\n[SQL: SELECT count(*) AS count_1 \nFROM (SELECT users.email AS users_email, users.hashed_password AS users_hashed_password, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.points AS users_points, users.last_login AS users_last_login, users.id AS users_id, users.created_at AS users_created_at, users.updated_at AS users_updated_at \nFROM users) AS anon_1]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "auth", "function": "get_auth_stats", "line": 183}
{"timestamp": "2025-06-21T11:05:41.407155", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 获取统计信息失败", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T11:06:31.581734", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T11:06:31.599309", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T11:06:31.600444", "level": "ERROR", "logger": "app.core.database", "message": "数据库初始化失败: name 'text' is not defined", "module": "database", "function": "init_database", "line": 79}
{"timestamp": "2025-06-21T11:09:57.090266", "level": "ERROR", "logger": "app.services.auth_service", "message": "查询用户失败: (sqlite3.OperationalError) no such table: users\n[SQL: SELECT users.email AS users_email, users.hashed_password AS users_hashed_password, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.points AS users_points, users.last_login AS users_last_login, users.id AS users_id, users.created_at AS users_created_at, users.updated_at AS users_updated_at \nFROM users \nWHERE users.email = ?\n LIMIT ? OFFSET ?]\n[parameters: ('<EMAIL>', 1, 0)]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "auth_service", "function": "get_user_by_email", "line": 90}
{"timestamp": "2025-06-21T11:09:57.092782", "level": "ERROR", "logger": "app.services.auth_service", "message": "创建用户失败: (sqlite3.OperationalError) no such table: users\n[SQL: SELECT count(*) AS count_1 \nFROM (SELECT users.email AS users_email, users.hashed_password AS users_hashed_password, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.points AS users_points, users.last_login AS users_last_login, users.id AS users_id, users.created_at AS users_created_at, users.updated_at AS users_updated_at \nFROM users) AS anon_1]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "auth_service", "function": "create_user", "line": 134}
{"timestamp": "2025-06-21T11:09:57.092782", "level": "ERROR", "logger": "app.api.auth", "message": "用户注册失败: (sqlite3.OperationalError) no such table: users\n[SQL: SELECT count(*) AS count_1 \nFROM (SELECT users.email AS users_email, users.hashed_password AS users_hashed_password, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.points AS users_points, users.last_login AS users_last_login, users.id AS users_id, users.created_at AS users_created_at, users.updated_at AS users_updated_at \nFROM users) AS anon_1]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "auth", "function": "register", "line": 34}
{"timestamp": "2025-06-21T11:09:57.093295", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: (sqlite3.OperationalError) no such table: users\n[SQL: SELECT count(*) AS count_1 \nFROM (SELECT users.email AS users_email, users.hashed_password AS users_hashed_password, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.points AS users_points, users.last_login AS users_last_login, users.id AS users_id, users.created_at AS users_created_at, users.updated_at AS users_updated_at \nFROM users) AS anon_1]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T11:11:15.267482", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T11:11:15.285333", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T11:11:15.286348", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T11:11:49.028231", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T11:11:49.046626", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T11:11:49.047633", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T11:12:05.963485", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T11:12:05.981470", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T11:12:05.982478", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T11:13:18.993153", "level": "ERROR", "logger": "app.services.auth_service", "message": "查询用户失败: (sqlite3.OperationalError) no such table: users\n[SQL: SELECT users.email AS users_email, users.hashed_password AS users_hashed_password, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.points AS users_points, users.last_login AS users_last_login, users.id AS users_id, users.created_at AS users_created_at, users.updated_at AS users_updated_at \nFROM users \nWHERE users.email = ?\n LIMIT ? OFFSET ?]\n[parameters: ('<EMAIL>', 1, 0)]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "auth_service", "function": "get_user_by_email", "line": 90}
{"timestamp": "2025-06-21T11:13:18.997215", "level": "ERROR", "logger": "app.services.auth_service", "message": "创建用户失败: (sqlite3.OperationalError) no such table: users\n[SQL: SELECT count(*) AS count_1 \nFROM (SELECT users.email AS users_email, users.hashed_password AS users_hashed_password, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.points AS users_points, users.last_login AS users_last_login, users.id AS users_id, users.created_at AS users_created_at, users.updated_at AS users_updated_at \nFROM users) AS anon_1]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "auth_service", "function": "create_user", "line": 134}
{"timestamp": "2025-06-21T11:13:18.997215", "level": "ERROR", "logger": "app.api.auth", "message": "用户注册失败: (sqlite3.OperationalError) no such table: users\n[SQL: SELECT count(*) AS count_1 \nFROM (SELECT users.email AS users_email, users.hashed_password AS users_hashed_password, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.points AS users_points, users.last_login AS users_last_login, users.id AS users_id, users.created_at AS users_created_at, users.updated_at AS users_updated_at \nFROM users) AS anon_1]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "auth", "function": "register", "line": 34}
{"timestamp": "2025-06-21T11:13:18.998729", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: (sqlite3.OperationalError) no such table: users\n[SQL: SELECT count(*) AS count_1 \nFROM (SELECT users.email AS users_email, users.hashed_password AS users_hashed_password, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.points AS users_points, users.last_login AS users_last_login, users.id AS users_id, users.created_at AS users_created_at, users.updated_at AS users_updated_at \nFROM users) AS anon_1]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T11:14:42.274437", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T11:14:42.346126", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T11:14:42.346126", "level": "ERROR", "logger": "app.core.database", "message": "数据库初始化失败: Not an executable object: 'SELECT 1'", "module": "database", "function": "init_database", "line": 79}
{"timestamp": "2025-06-21T11:15:05.930558", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T11:15:05.950507", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T11:15:05.950507", "level": "ERROR", "logger": "app.core.database", "message": "数据库初始化失败: Not an executable object: 'SELECT 1'", "module": "database", "function": "init_database", "line": 79}
{"timestamp": "2025-06-21T11:15:17.398347", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T11:15:17.416912", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T11:15:17.417922", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T11:15:33.660983", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T11:15:33.684963", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T11:15:33.685970", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T11:15:53.775225", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T11:15:53.794897", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T11:15:53.795894", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T11:17:25.464850", "level": "INFO", "logger": "app.services.auth_service", "message": "用户创建成功: <EMAIL>", "module": "auth_service", "function": "create_user", "line": 129}
{"timestamp": "2025-06-21T11:17:42.166548", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 未提供认证令牌", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T11:18:16.942571", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 未提供认证令牌", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T11:19:32.142260", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 认证失败", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T11:19:35.155192", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 认证失败", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T11:19:38.558044", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 未提供认证令牌", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T11:19:46.617609", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 未提供认证令牌", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T11:19:48.584754", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 未提供认证令牌", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T11:19:51.117939", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 未提供认证令牌", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T11:25:51.150629", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T11:25:51.173820", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T11:25:51.174912", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T11:25:52.324147", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T11:25:52.345535", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T11:25:52.346085", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T11:27:18.672092", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T11:27:18.692926", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T11:27:18.693933", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T11:27:19.849948", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T11:27:19.872076", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T11:27:19.872076", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T11:28:14.608815", "level": "INFO", "logger": "app.services.auth_service", "message": "用户创建成功: <EMAIL>", "module": "auth_service", "function": "create_user", "line": 129}
{"timestamp": "2025-06-21T11:28:45.835171", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 认证失败", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T15:19:23.535714", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T15:19:23.571254", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T15:19:23.571254", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T15:19:46.389052", "level": "ERROR", "logger": "app.services.auth_service", "message": "用户认证失败: 邮箱或密码错误", "module": "auth_service", "function": "authenticate_user", "line": 161}
{"timestamp": "2025-06-21T15:19:46.389764", "level": "ERROR", "logger": "app.api.auth", "message": "用户登录失败: 邮箱或密码错误", "module": "auth", "function": "login", "line": 68}
{"timestamp": "2025-06-21T15:19:46.390271", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱或密码错误", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T15:21:06.077745", "level": "INFO", "logger": "app.services.auth_service", "message": "用户创建成功: <EMAIL>", "module": "auth_service", "function": "create_user", "line": 129}
{"timestamp": "2025-06-21T15:21:14.010102", "level": "INFO", "logger": "app.services.auth_service", "message": "用户认证成功: <EMAIL>", "module": "auth_service", "function": "authenticate_user", "line": 157}
{"timestamp": "2025-06-21T15:21:14.012117", "level": "ERROR", "logger": "app.api.auth", "message": "用户登录失败: module 'jwt' has no attribute 'encode'", "module": "auth", "function": "login", "line": 68}
{"timestamp": "2025-06-21T15:21:14.013120", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱或密码错误", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T15:26:32.852245", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T15:26:32.877266", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T15:26:32.878417", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T15:27:08.530023", "level": "ERROR", "logger": "app.services.auth_service", "message": "用户认证失败: 邮箱或密码错误", "module": "auth_service", "function": "authenticate_user", "line": 161}
{"timestamp": "2025-06-21T15:27:08.530023", "level": "ERROR", "logger": "app.api.auth", "message": "用户登录失败: 邮箱或密码错误", "module": "auth", "function": "login", "line": 68}
{"timestamp": "2025-06-21T15:27:08.531539", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱或密码错误", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T15:27:21.086920", "level": "ERROR", "logger": "app.services.auth_service", "message": "创建用户失败: 邮箱已被注册", "module": "auth_service", "function": "create_user", "line": 134}
{"timestamp": "2025-06-21T15:27:21.086920", "level": "ERROR", "logger": "app.api.auth", "message": "用户注册失败: 邮箱已被注册", "module": "auth", "function": "register", "line": 34}
{"timestamp": "2025-06-21T15:27:21.088234", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱已被注册", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T15:27:31.385637", "level": "ERROR", "logger": "app.services.auth_service", "message": "用户认证失败: 邮箱或密码错误", "module": "auth_service", "function": "authenticate_user", "line": 161}
{"timestamp": "2025-06-21T15:27:31.386630", "level": "ERROR", "logger": "app.api.auth", "message": "用户登录失败: 邮箱或密码错误", "module": "auth", "function": "login", "line": 68}
{"timestamp": "2025-06-21T15:27:31.387135", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱或密码错误", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T15:28:18.892361", "level": "INFO", "logger": "app.services.auth_service", "message": "用户认证成功: <EMAIL>", "module": "auth_service", "function": "authenticate_user", "line": 157}
{"timestamp": "2025-06-21T15:28:38.991566", "level": "INFO", "logger": "app.services.auth_service", "message": "用户认证成功: <EMAIL>", "module": "auth_service", "function": "authenticate_user", "line": 157}
{"timestamp": "2025-06-21T15:29:55.736959", "level": "INFO", "logger": "app.services.auth_service", "message": "用户认证成功: <EMAIL>", "module": "auth_service", "function": "authenticate_user", "line": 157}
{"timestamp": "2025-06-21T15:35:51.380449", "level": "ERROR", "logger": "app.services.auth_service", "message": "用户认证失败: 邮箱或密码错误", "module": "auth_service", "function": "authenticate_user", "line": 161}
{"timestamp": "2025-06-21T15:35:51.381521", "level": "ERROR", "logger": "app.api.auth", "message": "用户登录失败: 邮箱或密码错误", "module": "auth", "function": "login", "line": 68}
{"timestamp": "2025-06-21T15:35:51.382550", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱或密码错误", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T15:36:01.274001", "level": "ERROR", "logger": "app.services.auth_service", "message": "创建用户失败: 邮箱已被注册", "module": "auth_service", "function": "create_user", "line": 134}
{"timestamp": "2025-06-21T15:36:01.274994", "level": "ERROR", "logger": "app.api.auth", "message": "用户注册失败: 邮箱已被注册", "module": "auth", "function": "register", "line": 34}
{"timestamp": "2025-06-21T15:36:01.274994", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱已被注册", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T15:36:24.630370", "level": "ERROR", "logger": "app.services.auth_service", "message": "用户认证失败: 邮箱或密码错误", "module": "auth_service", "function": "authenticate_user", "line": 161}
{"timestamp": "2025-06-21T15:36:24.631445", "level": "ERROR", "logger": "app.api.auth", "message": "用户登录失败: 邮箱或密码错误", "module": "auth", "function": "login", "line": 68}
{"timestamp": "2025-06-21T15:36:24.631445", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱或密码错误", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T15:36:53.844856", "level": "ERROR", "logger": "app.services.auth_service", "message": "用户认证失败: 邮箱或密码错误", "module": "auth_service", "function": "authenticate_user", "line": 161}
{"timestamp": "2025-06-21T15:36:53.845860", "level": "ERROR", "logger": "app.api.auth", "message": "用户登录失败: 邮箱或密码错误", "module": "auth", "function": "login", "line": 68}
{"timestamp": "2025-06-21T15:36:53.845860", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱或密码错误", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T15:41:07.719481", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T15:41:07.741825", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T15:41:07.742836", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T15:41:47.915466", "level": "INFO", "logger": "app.services.auth_service", "message": "用户认证成功: <EMAIL>", "module": "auth_service", "function": "authenticate_user", "line": 157}
{"timestamp": "2025-06-21T15:43:24.923352", "level": "INFO", "logger": "app.services.auth_service", "message": "用户认证成功: <EMAIL>", "module": "auth_service", "function": "authenticate_user", "line": 157}
{"timestamp": "2025-06-21T15:44:15.504501", "level": "INFO", "logger": "app.services.auth_service", "message": "用户认证成功: <EMAIL>", "module": "auth_service", "function": "authenticate_user", "line": 157}
{"timestamp": "2025-06-21T15:45:51.569103", "level": "ERROR", "logger": "app.services.auth_service", "message": "用户认证失败: 邮箱或密码错误", "module": "auth_service", "function": "authenticate_user", "line": 161}
{"timestamp": "2025-06-21T15:45:51.569103", "level": "ERROR", "logger": "app.api.auth", "message": "用户登录失败: 邮箱或密码错误", "module": "auth", "function": "login", "line": 68}
{"timestamp": "2025-06-21T15:45:51.570608", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱或密码错误", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T15:46:02.878987", "level": "INFO", "logger": "app.services.auth_service", "message": "用户认证成功: <EMAIL>", "module": "auth_service", "function": "authenticate_user", "line": 157}
{"timestamp": "2025-06-21T15:46:02.880997", "level": "ERROR", "logger": "app.api.auth", "message": "用户登录失败: module 'jwt' has no attribute 'encode'", "module": "auth", "function": "login", "line": 68}
{"timestamp": "2025-06-21T15:46:02.882406", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱或密码错误", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T15:46:13.740286", "level": "ERROR", "logger": "app.services.auth_service", "message": "创建用户失败: 邮箱已被注册", "module": "auth_service", "function": "create_user", "line": 134}
{"timestamp": "2025-06-21T15:46:13.740286", "level": "ERROR", "logger": "app.api.auth", "message": "用户注册失败: 邮箱已被注册", "module": "auth", "function": "register", "line": 34}
{"timestamp": "2025-06-21T15:46:13.740286", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱已被注册", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T15:46:44.856069", "level": "INFO", "logger": "app.services.auth_service", "message": "用户创建成功: <EMAIL>", "module": "auth_service", "function": "create_user", "line": 129}
{"timestamp": "2025-06-21T15:46:50.812229", "level": "ERROR", "logger": "app.services.auth_service", "message": "用户认证失败: 邮箱或密码错误", "module": "auth_service", "function": "authenticate_user", "line": 161}
{"timestamp": "2025-06-21T15:46:50.812229", "level": "ERROR", "logger": "app.api.auth", "message": "用户登录失败: 邮箱或密码错误", "module": "auth", "function": "login", "line": 68}
{"timestamp": "2025-06-21T15:46:50.813711", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱或密码错误", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T15:47:12.474714", "level": "INFO", "logger": "app.services.auth_service", "message": "用户认证成功: <EMAIL>", "module": "auth_service", "function": "authenticate_user", "line": 157}
{"timestamp": "2025-06-21T15:47:12.475717", "level": "ERROR", "logger": "app.api.auth", "message": "用户登录失败: module 'jwt' has no attribute 'encode'", "module": "auth", "function": "login", "line": 68}
{"timestamp": "2025-06-21T15:47:12.476778", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱或密码错误", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T16:09:34.856915", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T16:09:34.883082", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T16:09:34.883596", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T16:09:55.627090", "level": "INFO", "logger": "app.services.auth_service", "message": "用户认证成功: <EMAIL>", "module": "auth_service", "function": "authenticate_user", "line": 157}
{"timestamp": "2025-06-21T16:10:02.357034", "level": "INFO", "logger": "app.services.auth_service", "message": "用户积分更新成功: 4, 变化: 10, 当前: 110", "module": "auth_service", "function": "update_user_points", "line": 178}
{"timestamp": "2025-06-21T16:16:22.294711", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T16:16:22.315011", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T16:16:22.316016", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T16:16:48.660779", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T16:16:48.682650", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T16:16:48.683645", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T16:17:34.895580", "level": "ERROR", "logger": "app.services.auth_service", "message": "用户认证失败: 邮箱或密码错误", "module": "auth_service", "function": "authenticate_user", "line": 161}
{"timestamp": "2025-06-21T16:17:34.896085", "level": "ERROR", "logger": "app.api.auth", "message": "用户登录失败: 邮箱或密码错误", "module": "auth", "function": "login", "line": 68}
{"timestamp": "2025-06-21T16:17:34.896085", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱或密码错误", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T16:37:57.761858", "level": "ERROR", "logger": "app.services.auth_service", "message": "创建用户失败: 邮箱已被注册", "module": "auth_service", "function": "create_user", "line": 134}
{"timestamp": "2025-06-21T16:37:57.762865", "level": "ERROR", "logger": "app.api.auth", "message": "用户注册失败: 邮箱已被注册", "module": "auth", "function": "register", "line": 34}
{"timestamp": "2025-06-21T16:37:57.762865", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱已被注册", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T16:38:29.730311", "level": "INFO", "logger": "app.services.auth_service", "message": "用户创建成功: <EMAIL>", "module": "auth_service", "function": "create_user", "line": 129}
{"timestamp": "2025-06-21T16:39:34.925246", "level": "INFO", "logger": "app.services.auth_service", "message": "用户认证成功: <EMAIL>", "module": "auth_service", "function": "authenticate_user", "line": 157}
{"timestamp": "2025-06-21T16:39:45.695115", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 无效的令牌", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T16:39:52.708049", "level": "INFO", "logger": "app.services.auth_service", "message": "用户认证成功: <EMAIL>", "module": "auth_service", "function": "authenticate_user", "line": 157}
{"timestamp": "2025-06-21T16:40:02.477465", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 无效的令牌", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T16:40:11.514656", "level": "INFO", "logger": "app.services.auth_service", "message": "用户认证成功: <EMAIL>", "module": "auth_service", "function": "authenticate_user", "line": 157}
{"timestamp": "2025-06-21T16:40:11.531765", "level": "INFO", "logger": "app.services.auth_service", "message": "用户积分更新成功: 5, 变化: 10, 当前: 110", "module": "auth_service", "function": "update_user_points", "line": 178}
{"timestamp": "2025-06-21T16:40:21.483483", "level": "INFO", "logger": "app.services.auth_service", "message": "用户认证成功: <EMAIL>", "module": "auth_service", "function": "authenticate_user", "line": 157}
{"timestamp": "2025-06-21T16:42:08.453378", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T16:42:08.476367", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T16:42:08.477390", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T16:42:58.057237", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 未提供认证令牌", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T16:43:30.173313", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 未提供认证令牌", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T16:43:41.067319", "level": "ERROR", "logger": "app.services.auth_service", "message": "用户认证失败: 邮箱或密码错误", "module": "auth_service", "function": "authenticate_user", "line": 161}
{"timestamp": "2025-06-21T16:43:41.067828", "level": "ERROR", "logger": "app.api.auth", "message": "用户登录失败: 邮箱或密码错误", "module": "auth", "function": "login", "line": 68}
{"timestamp": "2025-06-21T16:43:41.067828", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱或密码错误", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T16:43:46.126126", "level": "INFO", "logger": "app.services.auth_service", "message": "用户认证成功: <EMAIL>", "module": "auth_service", "function": "authenticate_user", "line": 157}
{"timestamp": "2025-06-21T16:43:50.265014", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 未提供认证令牌", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T16:44:08.601603", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 未提供认证令牌", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T16:44:17.821319", "level": "INFO", "logger": "app.services.auth_service", "message": "用户积分更新成功: 4, 变化: 10, 当前: 120", "module": "auth_service", "function": "update_user_points", "line": 178}
{"timestamp": "2025-06-21T16:49:58.684549", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T16:49:58.706228", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T16:49:58.707228", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T17:02:00.211657", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T17:02:00.297204", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T17:02:00.299680", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T17:14:19.124990", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 无效的令牌", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T17:16:48.724015", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T17:16:48.795971", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T17:16:48.797994", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T17:17:44.069454", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T17:17:44.128073", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T17:17:44.130584", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T18:21:24.775295", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T18:21:24.809106", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T18:21:24.810117", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T18:22:55.072978", "level": "ERROR", "logger": "app.services.auth_service", "message": "创建用户失败: 邮箱已被注册", "module": "auth_service", "function": "create_user", "line": 134}
{"timestamp": "2025-06-21T18:22:55.072978", "level": "ERROR", "logger": "app.api.auth", "message": "用户注册失败: 邮箱已被注册", "module": "auth", "function": "register", "line": 34}
{"timestamp": "2025-06-21T18:22:55.072978", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱已被注册", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T18:28:56.492844", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/3ffcde2d-51ba-40bd-901d-b284eccc0b20/original.txt -> /files/users/4/tasks/3ffcde2d-51ba-40bd-901d-b284eccc0b20/original.txt", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T18:42:00.078046", "level": "ERROR", "logger": "app.services.auth_service", "message": "用户认证失败: 邮箱或密码错误", "module": "auth_service", "function": "authenticate_user", "line": 161}
{"timestamp": "2025-06-21T18:42:00.079052", "level": "ERROR", "logger": "app.api.auth", "message": "用户登录失败: 邮箱或密码错误", "module": "auth", "function": "login", "line": 68}
{"timestamp": "2025-06-21T18:42:00.079052", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱或密码错误", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T19:13:50.311306", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T19:13:50.340210", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T19:13:50.341732", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T19:14:21.575689", "level": "ERROR", "logger": "app.core.error_handler", "message": "Unhandled exception: workshop.html", "module": "error_handler", "function": "general_exception_handler", "line": 102}
{"timestamp": "2025-06-21T19:25:24.872963", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T19:25:24.899908", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T19:25:24.900908", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T19:25:47.738318", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/73aacfb5-442d-42a8-98d9-7dbb9b637319/original.txt -> /files/users/4/tasks/73aacfb5-442d-42a8-98d9-7dbb9b637319/original.txt", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T19:25:47.755146", "level": "INFO", "logger": "app.api.files", "message": "文件上传成功并创建任务: 1, 用户: 4", "module": "files", "function": "_upload_file_logic", "line": 108}
{"timestamp": "2025-06-21T19:25:47.755146", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 1 已加入处理队列", "module": "task_processing_service", "function": "start_task_processing", "line": 42}
{"timestamp": "2025-06-21T19:25:47.755146", "level": "INFO", "logger": "app.api.files", "message": "任务处理已启动: 1", "module": "files", "function": "_upload_file_logic", "line": 113}
{"timestamp": "2025-06-21T19:25:47.757265", "level": "INFO", "logger": "app.services.task_processing_service", "message": "开始处理任务: 1, 用户: 4", "module": "task_processing_service", "function": "_process_task", "line": 67}
{"timestamp": "2025-06-21T19:25:47.770162", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "开始音频转换任务: 1, 文件: data\\users\\4\\tasks\\73aacfb5-442d-42a8-98d9-7dbb9b637319\\original.txt", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 55}
{"timestamp": "2025-06-21T19:25:47.770162", "level": "INFO", "logger": "app.services.text_extraction_service", "message": "开始提取文本: data\\users\\4\\tasks\\73aacfb5-442d-42a8-98d9-7dbb9b637319\\original.txt, 类型: txt", "module": "text_extraction_service", "function": "extract_text_from_file", "line": 41}
{"timestamp": "2025-06-21T19:25:47.784674", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "文本分割完成，共 5 个段落", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 71}
{"timestamp": "2025-06-21T19:25:47.784674", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 1/5", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-21T19:25:50.291749", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_b061a359-039e-4a15-adde-f926d77c8974_segment_0001.mp3, 大小: 11808 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 228}
{"timestamp": "2025-06-21T19:25:50.294964", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/1/segment_0001.mp3 -> /files/users/4/tasks/1/segment_0001.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T19:25:50.305899", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 1 进度: 16% - 转换段落 1/5", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-21T19:25:50.305899", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 2/5", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-21T19:25:51.654119", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_f923a9e1-abc5-4121-8985-04a7daa4e9dc_segment_0002.mp3, 大小: 10080 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 228}
{"timestamp": "2025-06-21T19:25:51.657691", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/1/segment_0002.mp3 -> /files/users/4/tasks/1/segment_0002.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T19:25:51.669152", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 1 进度: 32% - 转换段落 2/5", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-21T19:25:51.669152", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 3/5", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-21T19:25:53.126779", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_4b89a609-d0fa-4f7b-9e2c-98aa6e437698_segment_0003.mp3, 大小: 60192 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 228}
{"timestamp": "2025-06-21T19:25:53.127782", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/1/segment_0003.mp3 -> /files/users/4/tasks/1/segment_0003.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T19:25:53.138721", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 1 进度: 48% - 转换段落 3/5", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-21T19:25:53.138721", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 4/5", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-21T19:25:54.242831", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_7378cc08-9cf0-4b1e-bb56-ee1eaf413d0f_segment_0004.mp3, 大小: 34560 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 228}
{"timestamp": "2025-06-21T19:25:54.245039", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/1/segment_0004.mp3 -> /files/users/4/tasks/1/segment_0004.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T19:25:54.255331", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 1 进度: 64% - 转换段落 4/5", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-21T19:25:54.256326", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 5/5", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-21T19:25:55.129861", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_ceb05cca-540d-4d1e-9105-41c28966cec8_segment_0005.mp3, 大小: 89712 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 228}
{"timestamp": "2025-06-21T19:25:55.131372", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/1/segment_0005.mp3 -> /files/users/4/tasks/1/segment_0005.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T19:25:55.145590", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 1 进度: 80% - 转换段落 5/5", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-21T19:25:55.148098", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/1/playlist_1.json -> /files/users/4/tasks/1/playlist_1.json", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T19:25:55.161450", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 1 进度: 90% - 生成播放列表", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-21T19:25:55.181895", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务处理完成: 1", "module": "task_processing_service", "function": "_process_task", "line": 119}
{"timestamp": "2025-06-21T19:26:09.086339", "level": "ERROR", "logger": "app.api.audio", "message": "获取音频库失败: 'Task' object has no attribute 'result_data'", "module": "audio", "function": "get_audio_library", "line": 84}
{"timestamp": "2025-06-21T19:26:09.087370", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 获取音频库失败", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T19:26:14.876422", "level": "ERROR", "logger": "app.api.audio", "message": "获取音频库失败: 'Task' object has no attribute 'result_data'", "module": "audio", "function": "get_audio_library", "line": 84}
{"timestamp": "2025-06-21T19:26:14.877426", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 获取音频库失败", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T19:27:02.390886", "level": "ERROR", "logger": "app.api.audio", "message": "获取音频库失败: 'Task' object has no attribute 'result_data'", "module": "audio", "function": "get_audio_library", "line": 84}
{"timestamp": "2025-06-21T19:27:02.392398", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 获取音频库失败", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T19:34:53.667529", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T19:34:53.696948", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T19:34:53.697956", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T19:35:31.468792", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 无效的令牌", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T20:53:11.177293", "level": "ERROR", "logger": "app.api.audio", "message": "获取音频库失败: 'Task' object has no attribute 'result_data'", "module": "audio", "function": "get_audio_library", "line": 84}
{"timestamp": "2025-06-21T20:53:11.178316", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 获取音频库失败", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T20:53:20.962850", "level": "ERROR", "logger": "app.api.audio", "message": "获取音频库失败: 'Task' object has no attribute 'result_data'", "module": "audio", "function": "get_audio_library", "line": 84}
{"timestamp": "2025-06-21T20:53:20.962850", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 获取音频库失败", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T20:53:31.231874", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/ac6dbafc-17e9-4019-bbfe-10e54646104a/original.txt -> /files/users/4/tasks/ac6dbafc-17e9-4019-bbfe-10e54646104a/original.txt", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T20:53:31.245118", "level": "INFO", "logger": "app.api.files", "message": "文件上传成功并创建任务: 2, 用户: 4", "module": "files", "function": "_upload_file_logic", "line": 108}
{"timestamp": "2025-06-21T20:53:31.245118", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 2 已加入处理队列", "module": "task_processing_service", "function": "start_task_processing", "line": 42}
{"timestamp": "2025-06-21T20:53:31.245118", "level": "INFO", "logger": "app.api.files", "message": "任务处理已启动: 2", "module": "files", "function": "_upload_file_logic", "line": 113}
{"timestamp": "2025-06-21T20:53:31.247149", "level": "INFO", "logger": "app.services.task_processing_service", "message": "开始处理任务: 2, 用户: 4", "module": "task_processing_service", "function": "_process_task", "line": 67}
{"timestamp": "2025-06-21T20:53:31.257641", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "开始音频转换任务: 2, 文件: data\\users\\4\\tasks\\ac6dbafc-17e9-4019-bbfe-10e54646104a\\original.txt", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 55}
{"timestamp": "2025-06-21T20:53:31.257641", "level": "INFO", "logger": "app.services.text_extraction_service", "message": "开始提取文本: data\\users\\4\\tasks\\ac6dbafc-17e9-4019-bbfe-10e54646104a\\original.txt, 类型: txt", "module": "text_extraction_service", "function": "extract_text_from_file", "line": 41}
{"timestamp": "2025-06-21T20:53:31.268883", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "文本分割完成，共 5 个段落", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 71}
{"timestamp": "2025-06-21T20:53:31.270390", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 1/5", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-21T20:53:33.956108", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_cf33edec-59e6-46fc-812c-e423e72e12fe_segment_0001.mp3, 大小: 11808 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 228}
{"timestamp": "2025-06-21T20:53:33.958724", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/2/segment_0001.mp3 -> /files/users/4/tasks/2/segment_0001.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T20:53:33.969888", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 2 进度: 16% - 转换段落 1/5", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-21T20:53:33.969888", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 2/5", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-21T20:53:34.932834", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_a658115a-a5fb-4729-98af-8545d8657f41_segment_0002.mp3, 大小: 10080 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 228}
{"timestamp": "2025-06-21T20:53:34.934476", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/2/segment_0002.mp3 -> /files/users/4/tasks/2/segment_0002.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T20:53:34.945600", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 2 进度: 32% - 转换段落 2/5", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-21T20:53:34.945600", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 3/5", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-21T20:53:35.894150", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_8b51072c-7c93-4160-96fb-c96cd003734a_segment_0003.mp3, 大小: 60192 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 228}
{"timestamp": "2025-06-21T20:53:35.897175", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/2/segment_0003.mp3 -> /files/users/4/tasks/2/segment_0003.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T20:53:35.908497", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 2 进度: 48% - 转换段落 3/5", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-21T20:53:35.909049", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 4/5", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-21T20:53:36.916422", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_631959e7-db27-4a71-8db3-a679c56ecec1_segment_0004.mp3, 大小: 34560 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 228}
{"timestamp": "2025-06-21T20:53:36.917425", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/2/segment_0004.mp3 -> /files/users/4/tasks/2/segment_0004.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T20:53:36.928759", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 2 进度: 64% - 转换段落 4/5", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-21T20:53:36.929795", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 5/5", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-21T20:53:38.213723", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_64c77030-f3ca-4d40-8fd5-560636f1238d_segment_0005.mp3, 大小: 89712 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 228}
{"timestamp": "2025-06-21T20:53:38.217545", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/2/segment_0005.mp3 -> /files/users/4/tasks/2/segment_0005.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T20:53:38.228906", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 2 进度: 80% - 转换段落 5/5", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-21T20:53:38.231970", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/2/playlist_2.json -> /files/users/4/tasks/2/playlist_2.json", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T20:53:38.242414", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 2 进度: 90% - 生成播放列表", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-21T20:53:38.253066", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务处理完成: 2", "module": "task_processing_service", "function": "_process_task", "line": 119}
{"timestamp": "2025-06-21T20:54:15.742492", "level": "ERROR", "logger": "app.api.audio", "message": "获取音频库失败: 'Task' object has no attribute 'result_data'", "module": "audio", "function": "get_audio_library", "line": 84}
{"timestamp": "2025-06-21T20:54:15.742998", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 获取音频库失败", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T20:57:35.235217", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T20:57:35.264221", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T20:57:35.265217", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T21:00:11.518751", "level": "ERROR", "logger": "app.services.auth_service", "message": "用户认证失败: 邮箱或密码错误", "module": "auth_service", "function": "authenticate_user", "line": 161}
{"timestamp": "2025-06-21T21:00:11.518751", "level": "ERROR", "logger": "app.api.auth", "message": "用户登录失败: 邮箱或密码错误", "module": "auth", "function": "login", "line": 68}
{"timestamp": "2025-06-21T21:00:11.520263", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱或密码错误", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T21:00:13.803672", "level": "ERROR", "logger": "app.services.auth_service", "message": "用户认证失败: 邮箱或密码错误", "module": "auth_service", "function": "authenticate_user", "line": 161}
{"timestamp": "2025-06-21T21:00:13.803672", "level": "ERROR", "logger": "app.api.auth", "message": "用户登录失败: 邮箱或密码错误", "module": "auth", "function": "login", "line": 68}
{"timestamp": "2025-06-21T21:00:13.804751", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱或密码错误", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T21:01:13.644782", "level": "INFO", "logger": "app.services.auth_service", "message": "用户认证成功: <EMAIL>", "module": "auth_service", "function": "authenticate_user", "line": 157}
{"timestamp": "2025-06-21T21:01:18.925965", "level": "INFO", "logger": "app.services.auth_service", "message": "用户积分更新成功: 4, 变化: 10, 当前: 130", "module": "auth_service", "function": "update_user_points", "line": 178}
{"timestamp": "2025-06-21T21:02:34.759606", "level": "ERROR", "logger": "app.api.audio", "message": "获取音频库失败: 'Task' object has no attribute 'result_data'", "module": "audio", "function": "get_audio_library", "line": 84}
{"timestamp": "2025-06-21T21:02:34.760614", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 获取音频库失败", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T21:02:35.560029", "level": "ERROR", "logger": "app.api.audio", "message": "获取音频库失败: 'Task' object has no attribute 'result_data'", "module": "audio", "function": "get_audio_library", "line": 84}
{"timestamp": "2025-06-21T21:02:35.561079", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 获取音频库失败", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T21:02:39.101310", "level": "ERROR", "logger": "app.api.audio", "message": "获取音频库失败: 'Task' object has no attribute 'result_data'", "module": "audio", "function": "get_audio_library", "line": 84}
{"timestamp": "2025-06-21T21:02:39.102596", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 获取音频库失败", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T21:07:34.485571", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T21:07:34.516330", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T21:07:34.516330", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T21:09:52.548647", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T21:09:52.589786", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T21:09:52.590798", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T21:09:55.145977", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T21:09:55.187989", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T21:09:55.188993", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T21:14:13.080542", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T21:14:13.110982", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T21:14:13.110982", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T21:15:43.403994", "level": "ERROR", "logger": "app.services.auth_service", "message": "用户认证失败: 邮箱或密码错误", "module": "auth_service", "function": "authenticate_user", "line": 161}
{"timestamp": "2025-06-21T21:15:43.404997", "level": "ERROR", "logger": "app.api.auth", "message": "用户登录失败: 邮箱或密码错误", "module": "auth", "function": "login", "line": 68}
{"timestamp": "2025-06-21T21:15:43.405999", "level": "WARNING", "logger": "app.core.error_handler", "message": "HTTPException: 邮箱或密码错误", "module": "error_handler", "function": "http_exception_handler", "line": 64}
{"timestamp": "2025-06-21T21:17:23.196090", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/9a2e0e85-c28c-4302-ad91-5b5f5ace077b/original.txt -> /files/users/4/tasks/9a2e0e85-c28c-4302-ad91-5b5f5ace077b/original.txt", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T21:17:23.214553", "level": "INFO", "logger": "app.api.files", "message": "文件上传成功并创建任务: 3, 用户: 4", "module": "files", "function": "_upload_file_logic", "line": 108}
{"timestamp": "2025-06-21T21:17:23.214553", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 3 已加入处理队列", "module": "task_processing_service", "function": "start_task_processing", "line": 42}
{"timestamp": "2025-06-21T21:17:23.214553", "level": "INFO", "logger": "app.api.files", "message": "任务处理已启动: 3", "module": "files", "function": "_upload_file_logic", "line": 113}
{"timestamp": "2025-06-21T21:17:23.217080", "level": "INFO", "logger": "app.services.task_processing_service", "message": "开始处理任务: 3, 用户: 4", "module": "task_processing_service", "function": "_process_task", "line": 67}
{"timestamp": "2025-06-21T21:17:23.231682", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "开始音频转换任务: 3, 文件: data\\users\\4\\tasks\\9a2e0e85-c28c-4302-ad91-5b5f5ace077b\\original.txt", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 55}
{"timestamp": "2025-06-21T21:17:23.231682", "level": "INFO", "logger": "app.services.text_extraction_service", "message": "开始提取文本: data\\users\\4\\tasks\\9a2e0e85-c28c-4302-ad91-5b5f5ace077b\\original.txt, 类型: txt", "module": "text_extraction_service", "function": "extract_text_from_file", "line": 41}
{"timestamp": "2025-06-21T21:17:23.238057", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "文本分割完成，共 1 个段落", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 71}
{"timestamp": "2025-06-21T21:17:23.239073", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 1/1", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-21T21:17:26.425903", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_d9894725-5420-4061-b73d-db2920b17a80_segment_0001.mp3, 大小: 207936 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 328}
{"timestamp": "2025-06-21T21:17:26.428981", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/3/segment_0001.mp3 -> /files/users/4/tasks/3/segment_0001.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T21:17:26.441896", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 3 进度: 80% - 转换段落 1/1", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-21T21:17:26.444031", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/3/playlist_3.json -> /files/users/4/tasks/3/playlist_3.json", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T21:17:26.456102", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 3 进度: 90% - 生成播放列表", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-21T21:17:26.467645", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务处理完成: 3", "module": "task_processing_service", "function": "_process_task", "line": 119}
{"timestamp": "2025-06-21T21:32:35.854455", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-21T21:32:35.884569", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-21T21:32:35.885581", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-21T21:35:45.066763", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/bf90114b-e7c7-4243-add6-de232a43d005/original.txt -> /files/users/4/tasks/bf90114b-e7c7-4243-add6-de232a43d005/original.txt", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T21:35:45.084082", "level": "INFO", "logger": "app.api.files", "message": "文件上传成功并创建任务: 4, 用户: 4", "module": "files", "function": "_upload_file_logic", "line": 108}
{"timestamp": "2025-06-21T21:35:45.084082", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 4 已加入处理队列", "module": "task_processing_service", "function": "start_task_processing", "line": 42}
{"timestamp": "2025-06-21T21:35:45.084082", "level": "INFO", "logger": "app.api.files", "message": "任务处理已启动: 4", "module": "files", "function": "_upload_file_logic", "line": 113}
{"timestamp": "2025-06-21T21:35:45.087283", "level": "INFO", "logger": "app.services.task_processing_service", "message": "开始处理任务: 4, 用户: 4", "module": "task_processing_service", "function": "_process_task", "line": 67}
{"timestamp": "2025-06-21T21:35:45.099899", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "开始音频转换任务: 4, 文件: data\\users\\4\\tasks\\bf90114b-e7c7-4243-add6-de232a43d005\\original.txt", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 55}
{"timestamp": "2025-06-21T21:35:45.099899", "level": "INFO", "logger": "app.services.text_extraction_service", "message": "开始提取文本: data\\users\\4\\tasks\\bf90114b-e7c7-4243-add6-de232a43d005\\original.txt, 类型: txt", "module": "text_extraction_service", "function": "extract_text_from_file", "line": 41}
{"timestamp": "2025-06-21T21:35:45.103439", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "文本分割完成，共 1 个段落", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 71}
{"timestamp": "2025-06-21T21:35:45.104456", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 1/1", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-21T21:35:47.197911", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_2f336e78-2291-4b67-9b0d-3080af95f313_segment_0001.mp3, 大小: 207936 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 328}
{"timestamp": "2025-06-21T21:35:47.201511", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/4/segment_0001.mp3 -> /files/users/4/tasks/4/segment_0001.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T21:35:47.212498", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 4 进度: 80% - 转换段落 1/1", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-21T21:35:47.215303", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/4/playlist_4.json -> /files/users/4/tasks/4/playlist_4.json", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T21:35:47.227559", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 4 进度: 90% - 生成播放列表", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-21T21:35:47.240982", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务处理完成: 4", "module": "task_processing_service", "function": "_process_task", "line": 119}
{"timestamp": "2025-06-21T21:47:45.811287", "level": "INFO", "logger": "app.services.auth_service", "message": "用户认证成功: <EMAIL>", "module": "auth_service", "function": "authenticate_user", "line": 157}
{"timestamp": "2025-06-21T21:48:22.772998", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/41ee083d-4bcd-45ed-9f15-e15c453b7ed9/original.txt -> /files/users/4/tasks/41ee083d-4bcd-45ed-9f15-e15c453b7ed9/original.txt", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T21:48:22.783608", "level": "INFO", "logger": "app.api.files", "message": "文件上传成功并创建任务: 5, 用户: 4", "module": "files", "function": "_upload_file_logic", "line": 108}
{"timestamp": "2025-06-21T21:48:22.784608", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 5 已加入处理队列", "module": "task_processing_service", "function": "start_task_processing", "line": 42}
{"timestamp": "2025-06-21T21:48:22.784608", "level": "INFO", "logger": "app.api.files", "message": "任务处理已启动: 5", "module": "files", "function": "_upload_file_logic", "line": 113}
{"timestamp": "2025-06-21T21:48:22.786060", "level": "INFO", "logger": "app.services.task_processing_service", "message": "开始处理任务: 5, 用户: 4", "module": "task_processing_service", "function": "_process_task", "line": 67}
{"timestamp": "2025-06-21T21:48:22.799004", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "开始音频转换任务: 5, 文件: data\\users\\4\\tasks\\41ee083d-4bcd-45ed-9f15-e15c453b7ed9\\original.txt", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 55}
{"timestamp": "2025-06-21T21:48:22.800004", "level": "INFO", "logger": "app.services.text_extraction_service", "message": "开始提取文本: data\\users\\4\\tasks\\41ee083d-4bcd-45ed-9f15-e15c453b7ed9\\original.txt, 类型: txt", "module": "text_extraction_service", "function": "extract_text_from_file", "line": 41}
{"timestamp": "2025-06-21T21:48:22.816204", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "文本分割完成，共 9 个段落", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 71}
{"timestamp": "2025-06-21T21:48:22.816204", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 1/9", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-21T21:48:25.526301", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_799c83fd-ad81-4493-beb9-b9ec8016f75e_segment_0001.mp3, 大小: 71856 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 328}
{"timestamp": "2025-06-21T21:48:25.527808", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/5/segment_0001.mp3 -> /files/users/4/tasks/5/segment_0001.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T21:48:25.537788", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 5 进度: 8% - 转换段落 1/9", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-21T21:48:25.538792", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 2/9", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-21T21:48:26.796718", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_46940b43-892e-404e-ba99-db46a4bb40fc_segment_0002.mp3, 大小: 55296 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 328}
{"timestamp": "2025-06-21T21:48:26.798737", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/5/segment_0002.mp3 -> /files/users/4/tasks/5/segment_0002.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T21:48:26.809300", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 5 进度: 17% - 转换段落 2/9", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-21T21:48:26.809300", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 3/9", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-21T21:48:27.796571", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_a5fe3e17-5134-48ba-acc8-008884690cf0_segment_0003.mp3, 大小: 114336 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 328}
{"timestamp": "2025-06-21T21:48:27.798649", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/5/segment_0003.mp3 -> /files/users/4/tasks/5/segment_0003.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T21:48:27.809116", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 5 进度: 26% - 转换段落 3/9", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-21T21:48:27.809116", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 4/9", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-21T21:48:29.245740", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_52113947-d9c9-4213-a9a9-4c073f3eebc6_segment_0004.mp3, 大小: 119808 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 328}
{"timestamp": "2025-06-21T21:48:29.247825", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/5/segment_0004.mp3 -> /files/users/4/tasks/5/segment_0004.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T21:48:29.259615", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 5 进度: 35% - 转换段落 4/9", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-21T21:48:29.259615", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 5/9", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-21T21:48:29.951316", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_fa2ebe98-170f-4afb-9d01-ca9caf34135f_segment_0005.mp3, 大小: 65664 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 328}
{"timestamp": "2025-06-21T21:48:29.954414", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/5/segment_0005.mp3 -> /files/users/4/tasks/5/segment_0005.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T21:48:29.965487", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 5 进度: 44% - 转换段落 5/9", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-21T21:48:29.965487", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 6/9", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-21T21:48:30.668544", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_b8ba37cc-ba11-42f5-b608-1f3f1b81dc5a_segment_0006.mp3, 大小: 57024 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 328}
{"timestamp": "2025-06-21T21:48:30.671057", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/5/segment_0006.mp3 -> /files/users/4/tasks/5/segment_0006.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T21:48:30.685257", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 5 进度: 53% - 转换段落 6/9", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-21T21:48:30.685257", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 7/9", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-21T21:48:31.967955", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_5fc5c5b5-239e-4c96-8102-49a0a24d97f6_segment_0007.mp3, 大小: 185616 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 328}
{"timestamp": "2025-06-21T21:48:31.971604", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/5/segment_0007.mp3 -> /files/users/4/tasks/5/segment_0007.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T21:48:31.983377", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 5 进度: 62% - 转换段落 7/9", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-21T21:48:31.983377", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 8/9", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-21T21:48:34.099520", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_18e63c32-a919-4d69-ba3d-b611ed934025_segment_0008.mp3, 大小: 292176 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 328}
{"timestamp": "2025-06-21T21:48:34.102093", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/5/segment_0008.mp3 -> /files/users/4/tasks/5/segment_0008.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T21:48:34.112398", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 5 进度: 71% - 转换段落 8/9", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-21T21:48:34.112398", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 9/9", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-21T21:48:35.435807", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_b8a9de37-df0b-473b-b032-b5faac1411eb_segment_0009.mp3, 大小: 171648 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 328}
{"timestamp": "2025-06-21T21:48:35.438412", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/5/segment_0009.mp3 -> /files/users/4/tasks/5/segment_0009.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T21:48:35.448690", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 5 进度: 80% - 转换段落 9/9", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-21T21:48:35.450840", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/5/playlist_5.json -> /files/users/4/tasks/5/playlist_5.json", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-21T21:48:35.462152", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 5 进度: 90% - 生成播放列表", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-21T21:48:35.473137", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务处理完成: 5", "module": "task_processing_service", "function": "_process_task", "line": 119}
{"timestamp": "2025-06-22T09:42:25.188087", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-22T09:42:25.220738", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-22T09:42:25.221741", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-22T09:42:35.641085", "level": "INFO", "logger": "app.services.auth_service", "message": "用户积分更新成功: 4, 变化: 10, 当前: 140", "module": "auth_service", "function": "update_user_points", "line": 178}
{"timestamp": "2025-06-22T09:42:51.745168", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/aa3e78e8-677e-4d48-9052-5f671b4f08ce/original.txt -> /files/users/4/tasks/aa3e78e8-677e-4d48-9052-5f671b4f08ce/original.txt", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-22T09:42:51.761299", "level": "INFO", "logger": "app.api.files", "message": "文件上传成功并创建任务: 6, 用户: 4", "module": "files", "function": "_upload_file_logic", "line": 108}
{"timestamp": "2025-06-22T09:42:51.761299", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 6 已加入处理队列", "module": "task_processing_service", "function": "start_task_processing", "line": 42}
{"timestamp": "2025-06-22T09:42:51.761299", "level": "INFO", "logger": "app.api.files", "message": "任务处理已启动: 6", "module": "files", "function": "_upload_file_logic", "line": 113}
{"timestamp": "2025-06-22T09:42:51.764854", "level": "INFO", "logger": "app.services.task_processing_service", "message": "开始处理任务: 6, 用户: 4", "module": "task_processing_service", "function": "_process_task", "line": 67}
{"timestamp": "2025-06-22T09:42:51.777355", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "开始音频转换任务: 6, 文件: data\\users\\4\\tasks\\aa3e78e8-677e-4d48-9052-5f671b4f08ce\\original.txt", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 55}
{"timestamp": "2025-06-22T09:42:51.777355", "level": "INFO", "logger": "app.services.text_extraction_service", "message": "开始提取文本: data\\users\\4\\tasks\\aa3e78e8-677e-4d48-9052-5f671b4f08ce\\original.txt, 类型: txt", "module": "text_extraction_service", "function": "extract_text_from_file", "line": 41}
{"timestamp": "2025-06-22T09:42:51.793306", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "文本分割完成，共 6 个段落", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 71}
{"timestamp": "2025-06-22T09:42:51.793306", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 1/6", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-22T09:43:14.522042", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_e934468e-9658-4e5e-a98a-3d0b2c764ce5_segment_0001.mp3, 大小: 228384 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 328}
{"timestamp": "2025-06-22T09:43:14.525095", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/6/segment_0001.mp3 -> /files/users/4/tasks/6/segment_0001.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-22T09:43:14.535801", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 6 进度: 13% - 转换段落 1/6", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-22T09:43:14.535801", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 2/6", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-22T09:43:16.086316", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_2641251c-b18b-4bfa-9a20-128b1f5b7aef_segment_0002.mp3, 大小: 112464 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 328}
{"timestamp": "2025-06-22T09:43:16.088364", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/6/segment_0002.mp3 -> /files/users/4/tasks/6/segment_0002.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-22T09:43:16.099826", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 6 进度: 26% - 转换段落 2/6", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-22T09:43:16.099826", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 3/6", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-22T09:43:17.624999", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_eb6ecc74-4c18-4136-8579-de0297bde93d_segment_0003.mp3, 大小: 92736 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 328}
{"timestamp": "2025-06-22T09:43:17.627061", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/6/segment_0003.mp3 -> /files/users/4/tasks/6/segment_0003.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-22T09:43:17.637920", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 6 进度: 40% - 转换段落 3/6", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-22T09:43:17.637920", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 4/6", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-22T09:43:19.151337", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_01bdcb8f-f7db-428f-87b6-34189d09f038_segment_0004.mp3, 大小: 96624 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 328}
{"timestamp": "2025-06-22T09:43:19.153462", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/6/segment_0004.mp3 -> /files/users/4/tasks/6/segment_0004.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-22T09:43:19.164897", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 6 进度: 53% - 转换段落 4/6", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-22T09:43:19.165046", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 5/6", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-22T09:43:19.884012", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_8db1f517-84be-4bd4-a2bf-9fdc96501423_segment_0005.mp3, 大小: 65232 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 328}
{"timestamp": "2025-06-22T09:43:19.885518", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/6/segment_0005.mp3 -> /files/users/4/tasks/6/segment_0005.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-22T09:43:19.895524", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 6 进度: 66% - 转换段落 5/6", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-22T09:43:19.895524", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 6/6", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-22T09:43:20.918887", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_51a0a89b-2a43-4d62-ab62-e619d1e22268_segment_0006.mp3, 大小: 123984 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 328}
{"timestamp": "2025-06-22T09:43:20.921395", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/6/segment_0006.mp3 -> /files/users/4/tasks/6/segment_0006.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-22T09:43:20.935205", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 6 进度: 80% - 转换段落 6/6", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-22T09:43:20.938228", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/6/playlist_6.json -> /files/users/4/tasks/6/playlist_6.json", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-22T09:43:20.950881", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 6 进度: 90% - 生成播放列表", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-22T09:43:20.967094", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务处理完成: 6", "module": "task_processing_service", "function": "_process_task", "line": 119}
{"timestamp": "2025-06-22T10:12:37.685756", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/d4dec1fa-c5ab-4ebc-b669-20e716cb3262/original.txt -> /files/users/4/tasks/d4dec1fa-c5ab-4ebc-b669-20e716cb3262/original.txt", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-22T10:12:37.696475", "level": "INFO", "logger": "app.api.files", "message": "文件上传成功并创建任务: 7, 用户: 4", "module": "files", "function": "_upload_file_logic", "line": 108}
{"timestamp": "2025-06-22T10:12:37.697480", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 7 已加入处理队列", "module": "task_processing_service", "function": "start_task_processing", "line": 42}
{"timestamp": "2025-06-22T10:12:37.697480", "level": "INFO", "logger": "app.api.files", "message": "任务处理已启动: 7", "module": "files", "function": "_upload_file_logic", "line": 113}
{"timestamp": "2025-06-22T10:12:37.698488", "level": "INFO", "logger": "app.services.task_processing_service", "message": "开始处理任务: 7, 用户: 4", "module": "task_processing_service", "function": "_process_task", "line": 67}
{"timestamp": "2025-06-22T10:12:37.709066", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "开始音频转换任务: 7, 文件: data\\users\\4\\tasks\\d4dec1fa-c5ab-4ebc-b669-20e716cb3262\\original.txt", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 55}
{"timestamp": "2025-06-22T10:12:37.709066", "level": "INFO", "logger": "app.services.text_extraction_service", "message": "开始提取文本: data\\users\\4\\tasks\\d4dec1fa-c5ab-4ebc-b669-20e716cb3262\\original.txt, 类型: txt", "module": "text_extraction_service", "function": "extract_text_from_file", "line": 41}
{"timestamp": "2025-06-22T10:12:37.711578", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "文本分割完成，共 6 个段落", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 71}
{"timestamp": "2025-06-22T10:12:37.711578", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 1/6", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-22T10:12:40.921508", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_28883574-de46-489c-b07b-c7789045789a_segment_0001.mp3, 大小: 167184 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 328}
{"timestamp": "2025-06-22T10:12:40.923509", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/7/segment_0001.mp3 -> /files/users/4/tasks/7/segment_0001.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-22T10:12:40.933342", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 7 进度: 13% - 转换段落 1/6", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-22T10:12:40.933860", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 2/6", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-22T10:12:42.239347", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_82ca0057-f46b-4ff7-bb85-5cf5829aff13_segment_0002.mp3, 大小: 195264 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 328}
{"timestamp": "2025-06-22T10:12:42.241392", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/7/segment_0002.mp3 -> /files/users/4/tasks/7/segment_0002.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-22T10:12:42.252107", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 7 进度: 26% - 转换段落 2/6", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-22T10:12:42.252107", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 3/6", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-22T10:12:43.676981", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_42b2fb5d-c98d-41e6-9f1b-b03a9d7a8075_segment_0003.mp3, 大小: 122256 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 328}
{"timestamp": "2025-06-22T10:12:43.680073", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/7/segment_0003.mp3 -> /files/users/4/tasks/7/segment_0003.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-22T10:12:43.692557", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 7 进度: 40% - 转换段落 3/6", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-22T10:12:43.692557", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 4/6", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-22T10:12:44.252900", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_462daa79-3bb4-4f6d-80e4-dc5e388d8359_segment_0004.mp3, 大小: 185616 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 328}
{"timestamp": "2025-06-22T10:12:44.254409", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/7/segment_0004.mp3 -> /files/users/4/tasks/7/segment_0004.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-22T10:12:44.264790", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 7 进度: 53% - 转换段落 4/6", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-22T10:12:44.264790", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 5/6", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-22T10:12:44.836042", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_a87b14f8-b3b4-48fd-a664-341881746fdc_segment_0005.mp3, 大小: 292176 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 328}
{"timestamp": "2025-06-22T10:12:44.839436", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/7/segment_0005.mp3 -> /files/users/4/tasks/7/segment_0005.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-22T10:12:44.851669", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 7 进度: 66% - 转换段落 5/6", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-22T10:12:44.851669", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 6/6", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-22T10:12:45.437546", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_db56b862-fdd6-4f0a-9911-7c5d9c1b008c_segment_0006.mp3, 大小: 171648 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 328}
{"timestamp": "2025-06-22T10:12:45.439605", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/7/segment_0006.mp3 -> /files/users/4/tasks/7/segment_0006.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-22T10:12:45.451915", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 7 进度: 80% - 转换段落 6/6", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-22T10:12:45.454953", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/7/playlist_7.json -> /files/users/4/tasks/7/playlist_7.json", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-22T10:12:45.465236", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 7 进度: 90% - 生成播放列表", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-22T10:12:45.476569", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务处理完成: 7", "module": "task_processing_service", "function": "_process_task", "line": 119}
{"timestamp": "2025-06-22T10:13:26.945287", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-22T10:13:26.973691", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-22T10:13:26.973691", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-22T10:13:42.415319", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/aa279d79-30e6-43fb-bb66-6981f43fdd63/original.txt -> /files/users/4/tasks/aa279d79-30e6-43fb-bb66-6981f43fdd63/original.txt", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-22T10:13:42.430246", "level": "INFO", "logger": "app.api.files", "message": "文件上传成功并创建任务: 8, 用户: 4", "module": "files", "function": "_upload_file_logic", "line": 108}
{"timestamp": "2025-06-22T10:13:42.430246", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 8 已加入处理队列", "module": "task_processing_service", "function": "start_task_processing", "line": 42}
{"timestamp": "2025-06-22T10:13:42.430246", "level": "INFO", "logger": "app.api.files", "message": "任务处理已启动: 8", "module": "files", "function": "_upload_file_logic", "line": 113}
{"timestamp": "2025-06-22T10:13:42.432753", "level": "INFO", "logger": "app.services.task_processing_service", "message": "开始处理任务: 8, 用户: 4", "module": "task_processing_service", "function": "_process_task", "line": 67}
{"timestamp": "2025-06-22T10:13:42.446156", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "开始音频转换任务: 8, 文件: data\\users\\4\\tasks\\aa279d79-30e6-43fb-bb66-6981f43fdd63\\original.txt", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 55}
{"timestamp": "2025-06-22T10:13:42.446156", "level": "INFO", "logger": "app.services.text_extraction_service", "message": "开始提取文本: data\\users\\4\\tasks\\aa279d79-30e6-43fb-bb66-6981f43fdd63\\original.txt, 类型: txt", "module": "text_extraction_service", "function": "extract_text_from_file", "line": 41}
{"timestamp": "2025-06-22T10:13:42.449715", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "文本长度 245 字符，小于等于分段阈值 1000，不进行分段", "module": "audio_conversion_service", "function": "_split_text_into_segments", "line": 150}
{"timestamp": "2025-06-22T10:13:42.450851", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "文本分割完成，共 1 个段落", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 71}
{"timestamp": "2025-06-22T10:13:42.450851", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 1/1", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-22T10:13:43.824911", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_4f15da2d-a4d5-40bd-9a61-c9f2cc15d1fe_segment_0001.mp3, 大小: 207936 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 335}
{"timestamp": "2025-06-22T10:13:43.827927", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/8/segment_0001.mp3 -> /files/users/4/tasks/8/segment_0001.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-22T10:13:43.839233", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 8 进度: 80% - 转换段落 1/1", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-22T10:13:43.841941", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/8/playlist_8.json -> /files/users/4/tasks/8/playlist_8.json", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-22T10:13:43.852922", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 8 进度: 90% - 生成播放列表", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-22T10:13:43.863762", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务处理完成: 8", "module": "task_processing_service", "function": "_process_task", "line": 119}
{"timestamp": "2025-06-22T10:14:16.644009", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/0477268c-e848-44e0-aa5e-6003db20d5ab/original.txt -> /files/users/4/tasks/0477268c-e848-44e0-aa5e-6003db20d5ab/original.txt", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-22T10:14:16.655975", "level": "INFO", "logger": "app.api.files", "message": "文件上传成功并创建任务: 9, 用户: 4", "module": "files", "function": "_upload_file_logic", "line": 108}
{"timestamp": "2025-06-22T10:14:16.656975", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 9 已加入处理队列", "module": "task_processing_service", "function": "start_task_processing", "line": 42}
{"timestamp": "2025-06-22T10:14:16.656975", "level": "INFO", "logger": "app.api.files", "message": "任务处理已启动: 9", "module": "files", "function": "_upload_file_logic", "line": 113}
{"timestamp": "2025-06-22T10:14:16.656975", "level": "INFO", "logger": "app.services.task_processing_service", "message": "开始处理任务: 9, 用户: 4", "module": "task_processing_service", "function": "_process_task", "line": 67}
{"timestamp": "2025-06-22T10:14:16.668017", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "开始音频转换任务: 9, 文件: data\\users\\4\\tasks\\0477268c-e848-44e0-aa5e-6003db20d5ab\\original.txt", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 55}
{"timestamp": "2025-06-22T10:14:16.669041", "level": "INFO", "logger": "app.services.text_extraction_service", "message": "开始提取文本: data\\users\\4\\tasks\\0477268c-e848-44e0-aa5e-6003db20d5ab\\original.txt, 类型: txt", "module": "text_extraction_service", "function": "extract_text_from_file", "line": 41}
{"timestamp": "2025-06-22T10:14:16.670296", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "文本长度 1533 字符，超过分段阈值 1000，开始分段处理", "module": "audio_conversion_service", "function": "_split_text_into_segments", "line": 153}
{"timestamp": "2025-06-22T10:14:16.671304", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "文本分割完成，共 2 个段落", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 71}
{"timestamp": "2025-06-22T10:14:16.671304", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 1/2", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-22T10:14:19.924169", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_b06afa36-5804-4414-ace9-5ac41284b723_segment_0001.mp3, 大小: 427392 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 335}
{"timestamp": "2025-06-22T10:14:19.926235", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/9/segment_0001.mp3 -> /files/users/4/tasks/9/segment_0001.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-22T10:14:19.944770", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 9 进度: 40% - 转换段落 1/2", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-22T10:14:19.944770", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 2/2", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-22T10:14:24.149994", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_d847bc15-ec20-4079-a7a6-28c63772210e_segment_0002.mp3, 大小: 718848 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 335}
{"timestamp": "2025-06-22T10:14:24.152623", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/9/segment_0002.mp3 -> /files/users/4/tasks/9/segment_0002.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-22T10:14:24.162394", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 9 进度: 80% - 转换段落 2/2", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-22T10:14:24.165490", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/9/playlist_9.json -> /files/users/4/tasks/9/playlist_9.json", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-22T10:14:24.174847", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 9 进度: 90% - 生成播放列表", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-22T10:14:24.185757", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务处理完成: 9", "module": "task_processing_service", "function": "_process_task", "line": 119}
{"timestamp": "2025-06-22T10:20:07.052980", "level": "INFO", "logger": "app.core.error_handler", "message": "错误处理器设置完成", "module": "error_handler", "function": "setup_error_handlers", "line": 217}
{"timestamp": "2025-06-22T10:20:07.081641", "level": "INFO", "logger": "app.core.database", "message": "数据库初始化成功: sqlite:///data/database/app.db", "module": "database", "function": "init_database", "line": 71}
{"timestamp": "2025-06-22T10:20:07.081641", "level": "INFO", "logger": "app.core.database", "message": "数据库连接测试成功", "module": "database", "function": "init_database", "line": 76}
{"timestamp": "2025-06-22T10:59:49.616339", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/23f92247-85bc-44d9-bfad-95d9c4211333/original.txt -> /files/users/4/tasks/23f92247-85bc-44d9-bfad-95d9c4211333/original.txt", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-22T10:59:49.637664", "level": "INFO", "logger": "app.api.files", "message": "文件上传成功并创建任务: 10, 用户: 4", "module": "files", "function": "_upload_file_logic", "line": 108}
{"timestamp": "2025-06-22T10:59:49.638173", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 10 已加入处理队列", "module": "task_processing_service", "function": "start_task_processing", "line": 42}
{"timestamp": "2025-06-22T10:59:49.638173", "level": "INFO", "logger": "app.api.files", "message": "任务处理已启动: 10", "module": "files", "function": "_upload_file_logic", "line": 113}
{"timestamp": "2025-06-22T10:59:49.640191", "level": "INFO", "logger": "app.services.task_processing_service", "message": "开始处理任务: 10, 用户: 4", "module": "task_processing_service", "function": "_process_task", "line": 67}
{"timestamp": "2025-06-22T10:59:49.651927", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "开始音频转换任务: 10, 文件: data\\users\\4\\tasks\\23f92247-85bc-44d9-bfad-95d9c4211333\\original.txt", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 55}
{"timestamp": "2025-06-22T10:59:49.651927", "level": "INFO", "logger": "app.services.text_extraction_service", "message": "开始提取文本: data\\users\\4\\tasks\\23f92247-85bc-44d9-bfad-95d9c4211333\\original.txt, 类型: txt", "module": "text_extraction_service", "function": "extract_text_from_file", "line": 41}
{"timestamp": "2025-06-22T10:59:49.670260", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "文本长度 1412 字符，超过分段阈值 1000，开始分段处理", "module": "audio_conversion_service", "function": "_split_text_into_segments", "line": 153}
{"timestamp": "2025-06-22T10:59:49.670260", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "文本分割完成，共 2 个段落", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 71}
{"timestamp": "2025-06-22T10:59:49.671257", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 1/2", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-22T10:59:54.410597", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_809ae50f-7236-4dfa-b659-df2b9d671ad1_segment_0001.mp3, 大小: 404784 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 335}
{"timestamp": "2025-06-22T10:59:54.413604", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/10/segment_0001.mp3 -> /files/users/4/tasks/10/segment_0001.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-22T10:59:54.431444", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 10 进度: 40% - 转换段落 1/2", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-22T10:59:54.432509", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "转换段落 2/2", "module": "audio_conversion_service", "function": "convert_file_to_audio", "line": 79}
{"timestamp": "2025-06-22T10:59:58.927310", "level": "INFO", "logger": "app.services.audio_conversion_service", "message": "TTS转换成功: data\\temp\\audio_84744bd5-0520-4476-8fe3-341757e98347_segment_0002.mp3, 大小: 312192 bytes", "module": "audio_conversion_service", "function": "_text_to_speech_zwei", "line": 335}
{"timestamp": "2025-06-22T10:59:58.928805", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/10/segment_0002.mp3 -> /files/users/4/tasks/10/segment_0002.mp3", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-22T10:59:58.940087", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 10 进度: 80% - 转换段落 2/2", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-22T10:59:58.942129", "level": "INFO", "logger": "app.services.storage_service", "message": "文件上传成功: users/4/tasks/10/playlist_10.json -> /files/users/4/tasks/10/playlist_10.json", "module": "storage_service", "function": "upload_file", "line": 228}
{"timestamp": "2025-06-22T10:59:58.952829", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务 10 进度: 90% - 生成播放列表", "module": "task_processing_service", "function": "_process_task", "line": 96}
{"timestamp": "2025-06-22T10:59:58.964368", "level": "INFO", "logger": "app.services.task_processing_service", "message": "任务处理完成: 10", "module": "task_processing_service", "function": "_process_task", "line": 119}
